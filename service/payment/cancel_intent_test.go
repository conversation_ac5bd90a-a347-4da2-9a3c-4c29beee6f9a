package payment

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestCanBeCancelled(t *testing.T) {
	tests := []struct {
		name     string
		status   domain.PaymentIntentStatus
		expected bool
	}{
		{
			name:     "pending status can be cancelled",
			status:   domain.PaymentIntentStatusPending,
			expected: true,
		},
		{
			name:     "success status cannot be cancelled",
			status:   domain.PaymentIntentStatusSuccess,
			expected: false,
		},
		{
			name:     "expired status cannot be cancelled",
			status:   domain.PaymentIntentStatusExpired,
			expected: false,
		},
		{
			name:     "insufficient_refunded status cannot be cancelled",
			status:   domain.PaymentIntentStatusInsufficientRefunded,
			expected: false,
		},
		{
			name:     "insufficient_not_refunded status cannot be cancelled",
			status:   domain.PaymentIntentStatusInsufficientNotRefunded,
			expected: false,
		},
		{
			name:     "cancelled status cannot be cancelled again",
			status:   domain.PaymentIntentStatusCancelled,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := canBeCancelled(tt.status)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCancelIntent(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
	mockCallbackExecutor := domain.NewMockCallbackExecutor(ctrl)

	// Initialize the service with mocks
	Init(mockRepo, mockCallbackExecutor, nil)

	// Initialize application service with mock to prevent panic in IntentStatusChanged
	mockAppRepo := domain.NewMockApplicationRepo(ctrl)
	mockAppRepo.EXPECT().GetApplicationOrgId(gomock.Any(), gomock.Any()).Return(1, nil).AnyTimes()
	mockAppRepo.EXPECT().GetApplication(gomock.Any(), gomock.Any()).Return(&domain.Application{
		ClientID:     "test-client",
		ClientSecret: "test-secret",
		Name:         "Test App",
		Domain:       "test.com",
	}, nil).AnyTimes()
	// Initialize application service to prevent panic in IntentStatusChanged
	application.Init(mockAppRepo)

	// Test data
	intentID := "test-intent-123"
	clientID := "test-client-456"
	wrongClientID := "wrong-client-789"

	// Create a test payment intent
	fiatAmount := decimal.NewFromFloat(100.0)
	fiatCurrency := "USD"
	testIntent := &domain.PaymentIntent{
		ID:              intentID,
		ClientID:        clientID,
		Status:          domain.PaymentIntentStatusPending,
		PaymentChain:    domain.Ethereum,
		PaymentAddress:  domain.NewEvmAddress("******************************************"),
		TokenAddress:    "******************************************",
		Symbol:          "USDC",
		Decimals:        6,
		CryptoAmount:    decimal.NewFromFloat(100.0),
		FiatAmount:      &fiatAmount,
		FiatCurrency:    &fiatCurrency,
		PricingMode:     "fiat",
		PaymentDeadline: time.Now().Add(30 * time.Minute),
		OrderData:       map[string]any{"order_id": "test-order"},
	}

	t.Run("successful cancellation", func(t *testing.T) {
		params := CancelIntentParams{
			IntentID: intentID,
			ClientID: clientID,
		}

		// Mock expectations
		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), intentID).
			Return(testIntent, nil)

		lockKey := fmt.Sprintf("payment:%s", intentID)
		blockTime := testIntent.PaymentChain.BlockTime()
		mockRepo.EXPECT().
			AcquireLock(gomock.Any(), lockKey, blockTime*10).
			Return(nil)

		mockRepo.EXPECT().
			ReleaseLock(gomock.Any(), lockKey)

		// Re-fetch after lock
		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), intentID).
			Return(testIntent, nil)

		// Update intent to cancelled
		mockRepo.EXPECT().
			UpdatePaymentIntent(gomock.Any(), intentID, gomock.Any()).
			DoAndReturn(func(ctx context.Context, id string, update *domain.PaymentIntentUpdate) error {
				assert.Equal(t, domain.PaymentIntentStatusCancelled, *update.Status)
				assert.NotNil(t, update.CancelledAt)
				return nil
			})

		// Call the function
		response, kgErr := CancelIntent(ctx, params)

		// Assertions
		assert.Nil(t, kgErr)
		assert.NotNil(t, response)
		assert.Equal(t, intentID, response.IntentID)
		assert.Equal(t, "cancelled", response.Status)
		assert.NotZero(t, response.CancelledAt)
	})

	t.Run("validation error - empty intent ID", func(t *testing.T) {
		params := CancelIntentParams{
			IntentID: "",
			ClientID: clientID,
		}

		response, kgErr := CancelIntent(ctx, params)

		assert.Nil(t, response)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
		assert.Equal(t, http.StatusBadRequest, kgErr.HttpStatus)
		assert.Contains(t, kgErr.Error.Error(), "intent ID is required")
	})

	t.Run("validation error - empty client ID", func(t *testing.T) {
		params := CancelIntentParams{
			IntentID: intentID,
			ClientID: "",
		}

		response, kgErr := CancelIntent(ctx, params)

		assert.Nil(t, response)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
		assert.Equal(t, http.StatusBadRequest, kgErr.HttpStatus)
		assert.Contains(t, kgErr.Error.Error(), "client ID is required")
	})

	t.Run("intent not found", func(t *testing.T) {
		params := CancelIntentParams{
			IntentID: "non-existent-intent",
			ClientID: clientID,
		}

		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), "non-existent-intent").
			Return(nil, domain.ErrRecordNotFound)

		response, kgErr := CancelIntent(ctx, params)

		assert.Nil(t, response)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.PaymentIntentNotFound, kgErr.Code)
		assert.Equal(t, http.StatusNotFound, kgErr.HttpStatus)
	})

	t.Run("database error on get intent", func(t *testing.T) {
		params := CancelIntentParams{
			IntentID: intentID,
			ClientID: clientID,
		}

		dbError := errors.New("database connection failed")
		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), intentID).
			Return(nil, dbError)

		response, kgErr := CancelIntent(ctx, params)

		assert.Nil(t, response)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.DBError, kgErr.Code)
		assert.Equal(t, http.StatusInternalServerError, kgErr.HttpStatus)
	})

	t.Run("client ID mismatch", func(t *testing.T) {
		params := CancelIntentParams{
			IntentID: intentID,
			ClientID: wrongClientID,
		}

		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), intentID).
			Return(testIntent, nil)

		response, kgErr := CancelIntent(ctx, params)

		assert.Nil(t, response)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
		assert.Equal(t, http.StatusBadRequest, kgErr.HttpStatus)
		assert.Contains(t, kgErr.Error.Error(), "invalid client id")
	})

	t.Run("lock acquisition failure", func(t *testing.T) {
		params := CancelIntentParams{
			IntentID: intentID,
			ClientID: clientID,
		}

		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), intentID).
			Return(testIntent, nil)

		lockKey := fmt.Sprintf("payment:%s", intentID)
		blockTime := testIntent.PaymentChain.BlockTime()
		mockRepo.EXPECT().
			AcquireLock(gomock.Any(), lockKey, blockTime*10).
			Return(domain.ErrLockNotAcquired)

		response, kgErr := CancelIntent(ctx, params)

		assert.Nil(t, response)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.InternalError, kgErr.Code)
		assert.Equal(t, http.StatusInternalServerError, kgErr.HttpStatus)
	})

	t.Run("intent already cancelled", func(t *testing.T) {
		params := CancelIntentParams{
			IntentID: intentID,
			ClientID: clientID,
		}

		cancelledIntent := &domain.PaymentIntent{
			ID:              intentID,
			ClientID:        clientID,
			Status:          domain.PaymentIntentStatusCancelled,
			PaymentChain:    domain.Ethereum,
			PaymentAddress:  domain.NewEvmAddress("******************************************"),
			TokenAddress:    "******************************************",
			Symbol:          "USDC",
			Decimals:        6,
			CryptoAmount:    decimal.NewFromFloat(100.0),
			FiatAmount:      &fiatAmount,
			FiatCurrency:    &fiatCurrency,
			PricingMode:     "fiat",
			PaymentDeadline: time.Now().Add(30 * time.Minute),
			OrderData:       map[string]any{"order_id": "test-order"},
		}

		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), intentID).
			Return(cancelledIntent, nil)

		lockKey := fmt.Sprintf("payment:%s", intentID)
		blockTime := cancelledIntent.PaymentChain.BlockTime()
		mockRepo.EXPECT().
			AcquireLock(gomock.Any(), lockKey, blockTime*10).
			Return(nil)

		mockRepo.EXPECT().
			ReleaseLock(gomock.Any(), lockKey)

		// Re-fetch after lock
		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), intentID).
			Return(cancelledIntent, nil)

		response, kgErr := CancelIntent(ctx, params)

		assert.Nil(t, response)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
		assert.Equal(t, http.StatusBadRequest, kgErr.HttpStatus)
		assert.Contains(t, kgErr.Error.Error(), "cannot be cancelled")
	})

	t.Run("intent with success status cannot be cancelled", func(t *testing.T) {
		params := CancelIntentParams{
			IntentID: intentID,
			ClientID: clientID,
		}

		successIntent := &domain.PaymentIntent{
			ID:              intentID,
			ClientID:        clientID,
			Status:          domain.PaymentIntentStatusSuccess,
			PaymentChain:    domain.Ethereum,
			PaymentAddress:  domain.NewEvmAddress("******************************************"),
			TokenAddress:    "******************************************",
			Symbol:          "USDC",
			Decimals:        6,
			CryptoAmount:    decimal.NewFromFloat(100.0),
			FiatAmount:      &fiatAmount,
			FiatCurrency:    &fiatCurrency,
			PricingMode:     "fiat",
			PaymentDeadline: time.Now().Add(30 * time.Minute),
			OrderData:       map[string]any{"order_id": "test-order"},
		}

		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), intentID).
			Return(successIntent, nil)

		lockKey := fmt.Sprintf("payment:%s", intentID)
		blockTime := successIntent.PaymentChain.BlockTime()
		mockRepo.EXPECT().
			AcquireLock(gomock.Any(), lockKey, blockTime*10).
			Return(nil)

		mockRepo.EXPECT().
			ReleaseLock(gomock.Any(), lockKey)

		// Re-fetch after lock
		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), intentID).
			Return(successIntent, nil)

		response, kgErr := CancelIntent(ctx, params)

		assert.Nil(t, response)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
		assert.Equal(t, http.StatusBadRequest, kgErr.HttpStatus)
		assert.Contains(t, kgErr.Error.Error(), "cannot be cancelled")
	})

	t.Run("database error on re-fetch after lock", func(t *testing.T) {
		params := CancelIntentParams{
			IntentID: intentID,
			ClientID: clientID,
		}

		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), intentID).
			Return(testIntent, nil)

		lockKey := fmt.Sprintf("payment:%s", intentID)
		blockTime := testIntent.PaymentChain.BlockTime()
		mockRepo.EXPECT().
			AcquireLock(gomock.Any(), lockKey, blockTime*10).
			Return(nil)

		mockRepo.EXPECT().
			ReleaseLock(gomock.Any(), lockKey)

		// Re-fetch after lock fails
		dbError := errors.New("database connection failed")
		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), intentID).
			Return(nil, dbError)

		response, kgErr := CancelIntent(ctx, params)

		assert.Nil(t, response)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.DBError, kgErr.Code)
		assert.Equal(t, http.StatusInternalServerError, kgErr.HttpStatus)
	})

	t.Run("database error on update intent", func(t *testing.T) {
		params := CancelIntentParams{
			IntentID: intentID,
			ClientID: clientID,
		}

		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), intentID).
			Return(testIntent, nil)

		lockKey := fmt.Sprintf("payment:%s", intentID)
		blockTime := testIntent.PaymentChain.BlockTime()
		mockRepo.EXPECT().
			AcquireLock(gomock.Any(), lockKey, blockTime*10).
			Return(nil)

		mockRepo.EXPECT().
			ReleaseLock(gomock.Any(), lockKey)

		// Re-fetch after lock
		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), intentID).
			Return(testIntent, nil)

		// Update intent fails
		dbError := errors.New("update failed")
		mockRepo.EXPECT().
			UpdatePaymentIntent(gomock.Any(), intentID, gomock.Any()).
			Return(dbError)

		response, kgErr := CancelIntent(ctx, params)

		assert.Nil(t, response)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.DBError, kgErr.Code)
		assert.Equal(t, http.StatusInternalServerError, kgErr.HttpStatus)
	})
}

// Test struct creation and field validation
func TestCancelIntentResponse(t *testing.T) {
	now := time.Now()

	response := &CancelIntentResponse{
		IntentID:    "test-intent-123",
		Status:      "cancelled",
		CancelledAt: now,
	}

	assert.Equal(t, "test-intent-123", response.IntentID)
	assert.Equal(t, "cancelled", response.Status)
	assert.Equal(t, now, response.CancelledAt)
}

func TestCancelIntentParams(t *testing.T) {
	params := CancelIntentParams{
		IntentID: "test-intent-456",
		ClientID: "test-client-789",
	}

	assert.Equal(t, "test-intent-456", params.IntentID)
	assert.Equal(t, "test-client-789", params.ClientID)
}
